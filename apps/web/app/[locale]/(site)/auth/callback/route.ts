import { NextResponse } from 'next/server';
import { getLocale } from 'next-intl/server';

import pathsConfig from '@pdfily/config/paths.config';
import { createAuthCallbackService } from '@pdfily/supabase/auth';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';

import { redirect } from '@/lib/i18n/navigation';

/**
 * Handles GET requests for the auth callback.
 *
 * This route is required for the server-side authentication flow implemented by the SSR package.
 * It exchanges an authorization code for the user's session.
 * For more details, see: https://supabase.com/docs/guides/auth/server-side/nextjs
 *
 * @param {Request} request - The incoming HTTP request object.
 * @returns {Promise<NextResponse>} A promise that resolves to a NextResponse, redirecting the user appropriately.
 */
export async function GET(request: Request): Promise<NextResponse> {
  const client = getSupabaseServerClient();
  const service = createAuthCallbackService(client);
  const url = new URL(request.url);
  const isPopup = url.searchParams.get('popup') === 'true';

  const { nextPath, success, error } = await service.exchangeCodeForSession(request, {
    redirectPath: pathsConfig.app.home,
  });

  // Si c'est une popup, retourner du HTML avec JavaScript pour communiquer avec la fenêtre parent
  if (isPopup) {
    if (success) {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>Authentication Success</title></head>
        <body>
          <script>
            console.log('Popup authentication successful, sending message to parent');
            if (window.opener) {
              window.opener.postMessage({
                type: 'GOOGLE_AUTH_SUCCESS',
                redirectTo: '${nextPath}'
              }, window.location.origin);
            }
            window.close();
          </script>
          <p>Authentication successful. This window will close automatically.</p>
        </body>
        </html>
      `;
      return new NextResponse(html, {
        headers: { 'Content-Type': 'text/html' },
      });
    } else {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>Authentication Error</title></head>
        <body>
          <script>
            console.log('Popup authentication failed, sending error to parent');
            if (window.opener) {
              window.opener.postMessage({
                type: 'GOOGLE_AUTH_ERROR',
                error: '${error || 'Authentication failed'}'
              }, window.location.origin);
            }
            window.close();
          </script>
          <p>Authentication failed. This window will close automatically.</p>
        </body>
        </html>
      `;
      return new NextResponse(html, {
        headers: { 'Content-Type': 'text/html' },
      });
    }
  }

  // Comportement normal pour les redirections non-popup
  const locale = await getLocale();
  return redirect({ href: nextPath, locale });
}
