'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * PopupCallback component handles the OAuth callback in a popup window.
 *
 * Cette page est appelée après que l'utilisateur s'authentifie avec Google.
 * Elle vérifie si l'authentification a réussi et envoie le résultat à la fenêtre parent.
 */
export default function PopupCallback() {
  const [mounted, setMounted] = useState(false);
  const [processing, setProcessing] = useState(false);
  const searchParams = useSearchParams();

  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const redirectTo = searchParams.get('redirect_to') || '/';

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || processing) return;

    const processAuth = async () => {
      setProcessing(true);

      try {
        if (error) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: errorDescription || 'Authentication failed',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        if (!code) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: 'No authorization code received',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        console.log('OAuth successful, code received:', code);

        // Redirection directe vers le callback serveur (préserve les cookies de session)
        const callbackUrl = `/auth/callback?code=${code}&redirect_to=${encodeURIComponent(redirectTo)}&popup=true`;

        console.log('Redirecting popup to server callback:', callbackUrl);
        window.location.href = callbackUrl;
      } catch (err) {
        console.error('Auth processing error:', err);

        // Send error to parent window
        if (window.opener) {
          window.opener.postMessage(
            {
              type: 'GOOGLE_AUTH_ERROR',
              error: 'Failed to complete authentication',
            },
            window.location.origin,
          );
        }

        setTimeout(() => window.close(), 500);
      }
    };

    processAuth();
  }, [mounted, processing, code, error, errorDescription, redirectTo]);

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">Processing authentication...</p>
      </div>
    </div>
  );
}
