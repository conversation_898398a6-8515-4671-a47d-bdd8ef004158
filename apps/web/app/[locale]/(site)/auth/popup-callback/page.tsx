'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * PopupCallback component handles the OAuth callback in a popup window.
 *
 * This component:
 * 1. Processes the OAuth callback using the standard Supabase flow
 * 2. Sends success/error status to the parent window via BroadcastChannel
 * 3. Closes the popup window
 */
export default function PopupCallback() {
  const [mounted, setMounted] = useState(false);
  const [processing, setProcessing] = useState(false);
  const searchParams = useSearchParams();

  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const redirectTo = searchParams.get('redirect_to') || '/';

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || processing) return;

    const processAuth = async () => {
      setProcessing(true);

      try {
        if (error) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: errorDescription || 'Authentication failed',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        if (!code) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: 'No authorization code received',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        console.log('Processing authentication with code:', code);
        console.log('Redirect to:', redirectTo);
        console.log('Window opener exists:', !!window.opener);
        console.log('Window opener closed:', window.opener?.closed);

        // Send the code to the parent window to handle the exchange there
        // This is important because the flow state is stored in the parent window's session
        if (window.opener && !window.opener.closed) {
          console.log('Sending code to parent window...');

          window.opener.postMessage(
            {
              type: 'GOOGLE_AUTH_CODE',
              code: code,
              redirectTo: redirectTo,
            },
            window.location.origin,
          );

          console.log('Code sent to parent window, closing popup in 500ms');

          // Close the popup after sending the code
          setTimeout(() => {
            console.log('Closing popup now');
            window.close();
          }, 500);
        } else {
          console.log('No opener window found or opener is closed, closing popup');
          setTimeout(() => {
            window.close();
          }, 1000);
        }
      } catch (err) {
        console.error('Auth processing error:', err);

        // Send error to parent window
        if (window.opener) {
          window.opener.postMessage(
            {
              type: 'GOOGLE_AUTH_ERROR',
              error: 'Failed to complete authentication',
            },
            window.location.origin,
          );
        }

        setTimeout(() => window.close(), 500);
      }
    };

    processAuth();
  }, [mounted, processing, code, error, errorDescription, redirectTo]);

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">Processing authentication...</p>
      </div>
    </div>
  );
}
