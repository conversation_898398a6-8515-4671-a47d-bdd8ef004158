'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * PopupCallback component handles the OAuth callback in a popup window.
 *
 * Cette page est appelée après que l'utilisateur s'authentifie avec Google.
 * Elle vérifie si l'authentification a réussi et envoie le résultat à la fenêtre parent.
 */
export default function PopupCallback() {
  const [mounted, setMounted] = useState(false);
  const [processing, setProcessing] = useState(false);
  const searchParams = useSearchParams();

  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const redirectTo = searchParams.get('redirect_to') || '/';

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || processing) return;

    const processAuth = async () => {
      setProcessing(true);

      try {
        if (error) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: errorDescription || 'Authentication failed',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        if (!code) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: 'No authorization code received',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        console.log('OAuth successful, code received:', code);

        // Rediriger vers le callback serveur qui gère l'échange ET le transfert
        const callbackUrl = `/auth/callback?code=${code}&redirect_to=${encodeURIComponent(redirectTo)}`;

        // Faire la requête au callback serveur
        try {
          const response = await fetch(callbackUrl, {
            method: 'GET',
            credentials: 'include', // Important pour les cookies de session
          });

          if (response.ok) {
            // Succès - envoyer le message à la fenêtre parent
            if (window.opener && !window.opener.closed) {
              console.log('Server callback successful, sending success to parent window');

              window.opener.postMessage(
                {
                  type: 'GOOGLE_AUTH_SUCCESS',
                  redirectTo: redirectTo,
                },
                window.location.origin,
              );
            }
          } else {
            throw new Error('Server callback failed');
          }
        } catch (callbackError) {
          console.error('Server callback error:', callbackError);

          // Envoyer l'erreur à la fenêtre parent
          if (window.opener && !window.opener.closed) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: 'Authentication processing failed',
              },
              window.location.origin,
            );
          }
        }

        // Fermer la popup après traitement
        setTimeout(() => {
          console.log('Closing popup now');
          window.close();
        }, 500);
      } catch (err) {
        console.error('Auth processing error:', err);

        // Send error to parent window
        if (window.opener) {
          window.opener.postMessage(
            {
              type: 'GOOGLE_AUTH_ERROR',
              error: 'Failed to complete authentication',
            },
            window.location.origin,
          );
        }

        setTimeout(() => window.close(), 500);
      }
    };

    processAuth();
  }, [mounted, processing, code, error, errorDescription, redirectTo]);

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">Processing authentication...</p>
      </div>
    </div>
  );
}
