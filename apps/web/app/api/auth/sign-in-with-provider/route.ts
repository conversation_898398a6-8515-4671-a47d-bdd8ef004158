import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import appConfig from '@pdfily/config/app.config';
import pathsConfig from '@pdfily/config/paths.config';

/**
 * Handles OAuth sign-in request using Supabase authentication.
 *
 * This endpoint expects a JSON body containing:
 * - `provider` (optional): the OAuth provider to use (default is `'google'`)
 * - `path`: the redirect path after successful authentication
 *
 * It returns a JSON response containing either:
 * - `url`: the URL to redirect the user to the provider's auth page
 * - or `error`: an error message in case of failure
 *
 * @param {NextRequest} request - The incoming POST request
 * @returns {Promise<NextResponse>} A JSON response with the OAuth redirect URL or an error
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { provider = 'google', path, usePopup = false } = await request.json();

    // Pour les popups, utilisons un client sans PKCE
    let supabase;
    if (usePopup) {
      const { createClient } = await import('@supabase/supabase-js');
      supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
        auth: {
          flowType: 'pkce',
          autoRefreshToken: false,
          persistSession: false, // Important : pas de session persistante pour les popups
        },
      });
    } else {
      supabase = getSupabaseServerClient();
    }

    // Determine the callback URL based on whether we're using popup mode
    let callbackUrl: URL;

    if (usePopup) {
      // For popup mode, use the popup callback page
      callbackUrl = new URL('/auth/popup-callback', appConfig.url);
      // Add the redirect path as a parameter so we can use it after auth
      callbackUrl.searchParams.set('redirect_to', path);
      callbackUrl.searchParams.set('popup', 'true'); // Flag to indicate this is a popup flow
    } else {
      // For regular mode, use the standard callback
      callbackUrl = new URL(pathsConfig.auth.callback, appConfig.url);
      callbackUrl.searchParams.set('redirect_to', path);
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: callbackUrl.toString(),
      },
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ url: data.url });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' + error }, { status: 500 });
  }
}
