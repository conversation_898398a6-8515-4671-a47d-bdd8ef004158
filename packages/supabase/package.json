{"name": "@pdfily/supabase", "private": true, "version": "0.1.0", "description": "Supabase integration and utilities for the PDFily monorepo.", "type": "module", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.846.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@pdfily/config": "workspace:*", "@pdfily/shared": "workspace:*", "@smithy/types": "^4.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "2.49.8", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "next": "^15.4.4", "react": "^19.1.0", "server-only": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/uuid": "^10.0.0", "typescript": "5.8.3"}, "exports": {"./server-client": "./src/clients/server-client.ts", "./server-admin-client": "./src/clients/server-admin-client.ts", "./middleware-client": "./src/clients/middleware-client.ts", "./browser-client": "./src/clients/browser-client.ts", "./s3-client": "./src/clients/s3-client.ts", "./s3/*": "./src/lib/s3/*.ts", "./database": "./src/database.types.ts", "./hooks/*": "./src/hooks/*.ts", "./auth": "./src/auth.ts", "./types": "./src/types.ts"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}