'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ComponentProps, useEffect, useState, useCallback } from 'react';
import { cn } from '@pdfily/ui/utils';
import authConfig from '@pdfily/config/auth.config';

import Swal from 'sweetalert2';

import { Button } from '../ui/button';
import { getSupabaseBrowserClient } from '@pdfily/supabase/browser-client';

/**
 * Props interface for the GoogleAuthButton component.
 *
 * @interface GoogleAuthButtonProps
 * @extends {React.ButtonHTMLAttributes<HTMLButtonElement>}
 */
type GoogleAuthButtonProps = ComponentProps<typeof Button> & {
  /**
   * Additional CSS classes to apply to the button.
   * @type {string}
   * @default ''
   */
  className?: string;

  path: string;
};

/**
 * GoogleAuthButton component that renders a Google authentication button with visual separator.
 *
 * The component only renders when Google authentication is enabled in the auth configuration.
 * When disabled, it returns an empty fragment to maintain layout consistency.
 * Uses popup-based authentication to avoid page redirects.
 */
export default function GoogleAuthButton({ path, className = '', ...rest }: GoogleAuthButtonProps) {
  const t = useTranslations('Auth');
  const { googleAuthEnabled } = authConfig;
  const [popup, setPopup] = useState<Window | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handlePopupMessage = useCallback(
    (event: MessageEvent) => {
      console.log('Received message from popup:', event.data);

      // check origin for security
      if (event.origin !== window.location.origin) {
        console.log('Message origin mismatch:', event.origin, 'vs', window.location.origin);
        return;
      }

      const { type, error, redirectTo } = event.data;

      // Only handle our specific message types
      if (type !== 'GOOGLE_AUTH_SUCCESS' && type !== 'GOOGLE_AUTH_ERROR') {
        return;
      }

      setPopup(null);

      if (type === 'GOOGLE_AUTH_ERROR') {
        setIsLoading(false);
        console.error('Authentication error from popup:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Failed to sign in with Google: ${error}`,
        });
        return;
      }

      if (type === 'GOOGLE_AUTH_SUCCESS') {
        setIsLoading(false);
        console.log('Authentication successful, redirecting to:', redirectTo || path);
        window.location.href = redirectTo || path;
        return;
      }
    },
    [setIsLoading, setPopup, path],
  );

  useEffect(() => {
    // If there is no popup, nothing to do
    if (!popup) return;

    console.log('Setting up popup listeners');

    // Listen for messages from the popup window
    window.addEventListener('message', handlePopupMessage);

    // Check if popup is closed manually
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        console.log('Popup was closed manually');
        setIsLoading(false);
        setPopup(null);
        clearInterval(checkClosed);
      }
    }, 1000);

    // effect cleaner (when component unmount)
    return () => {
      console.log('Cleaning up popup listeners');
      window.removeEventListener('message', handlePopupMessage);
      clearInterval(checkClosed);
      setPopup(null);
    };
  }, [popup, handlePopupMessage]);

  const handleGoogleSignIn = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Open popup immediately to avoid Safari blocking
      const popup = openPopup('about:blank');
      setPopup(popup);

      // Use browser client to initiate OAuth flow (maintains PKCE state in browser)
      const supabase = getSupabaseBrowserClient();

      // Create callback URL for popup
      const callbackUrl = new URL('/auth/popup-callback', window.location.origin);
      callbackUrl.searchParams.set('redirect_to', path);
      callbackUrl.searchParams.set('popup', 'true');

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: callbackUrl.toString(),
        },
      });

      if (error) {
        popup?.close();
        setPopup(null);
        throw new Error(error.message);
      }

      if (data.url) {
        // Navigate the already opened popup to the auth URL
        if (popup && !popup.closed) {
          popup.location.href = data.url;
        } else {
          throw new Error('Popup was blocked or closed');
        }
      } else {
        popup?.close();
        setPopup(null);
        throw new Error('No authentication URL received');
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      setIsLoading(false);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to sign in with Google. Please try again.',
      });
    }
  };

  const openPopup = (url: string) => {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    // window features for popup
    const windowFeatures = `scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`;
    const popup = window.open(url, 'google-auth-popup', windowFeatures);
    return popup;
  };

  return (
    <>
      {googleAuthEnabled ? (
        <div>
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className={cn(
              'flex gap-x-2 justify-center items-center p-3 border w-full border-gray-200 hover:bg-gray-50 hover:shadow-md hover:border-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
              className,
            )}
            {...rest}
          >
            <Image src="/images/auth/google.svg" alt="google" width={500} height={28} className="w-7 h-7" />
            <p>
              {t('loginWithGoogle')} {isLoading ? '...' : ''}
            </p>
          </button>
          <div className="flex items-center my-4">
            <div className="flex-grow h-px bg-gray-300" />
            <span className="px-4 text-sm text-gray-500">OR</span>
            <div className="flex-grow h-px bg-gray-300" />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
}
