'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * PopupCallback component handles the OAuth callback in a popup window.
 *
 * This component:
 * 1. Processes the OAuth callback using the standard Supabase flow
 * 2. Sends success/error status to the parent window via BroadcastChannel
 * 3. Closes the popup window
 */
export default function PopupCallback() {
  const [mounted, setMounted] = useState(false);
  const [processing, setProcessing] = useState(false);
  const searchParams = useSearchParams();

  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const redirectTo = searchParams.get('redirect_to') || '/';

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || processing) return;

    const processAuth = async () => {
      setProcessing(true);

      try {
        if (error) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: errorDescription || 'Authentication failed',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        if (!code) {
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: 'No authorization code received',
              },
              window.location.origin,
            );
          }
          setTimeout(() => window.close(), 500);
          return;
        }

        console.log('Processing authentication with code:', code);
        console.log('Redirect to:', redirectTo);
        console.log('Window opener exists:', !!window.opener);
        console.log('Window opener closed:', window.opener?.closed);

        // Exchange the code for session in the popup window where PKCE state exists
        console.log('Exchanging code for session in popup window...');

        // Import the necessary modules
        const { getSupabaseBrowserClient } = await import('@pdfily/supabase/browser-client');
        const { createPopupAuthService } = await import('@pdfily/supabase/popup-auth.service');

        // Get browser client (has PKCE state in this popup window)
        const supabase = getSupabaseBrowserClient();

        // Get current session for document transfer
        const { data: currentSession } = await supabase.auth.getSession();
        const anonymousUserId = currentSession?.session?.user?.id;

        // Create popup auth service
        const authService = createPopupAuthService(supabase);

        // Exchange code for session using PKCE
        const result = await authService.exchangeCodeForSession(code, anonymousUserId);

        if (result.success) {
          console.log('Authentication successful in popup, sending success to parent');

          // Send success to parent window
          if (window.opener && !window.opener.closed) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_SUCCESS',
                redirectTo: redirectTo,
                user: result.user,
              },
              window.location.origin,
            );
          }
        } else {
          console.error('Authentication failed in popup:', result.error);

          // Send error to parent window
          if (window.opener && !window.opener.closed) {
            window.opener.postMessage(
              {
                type: 'GOOGLE_AUTH_ERROR',
                error: result.error || 'Authentication failed',
              },
              window.location.origin,
            );
          }
        }

        // Close the popup after processing
        setTimeout(() => {
          console.log('Closing popup after authentication processing');
          window.close();
        }, 500);
      } catch (err) {
        console.error('Auth processing error:', err);

        // Send error to parent window
        if (window.opener) {
          window.opener.postMessage(
            {
              type: 'GOOGLE_AUTH_ERROR',
              error: 'Failed to complete authentication',
            },
            window.location.origin,
          );
        }

        setTimeout(() => window.close(), 500);
      }
    };

    processAuth();
  }, [mounted, processing, code, error, errorDescription, redirectTo]);

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">Processing authentication...</p>
      </div>
    </div>
  );
}
